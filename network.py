"""
Neural Network Module

This module contains the implementation of the bipartite neural network with
spike-timing dependent plasticity (STDP) for the Basal Ganglia Oscillator simulation.

Classes:
    BipartiteNetwork: A two-layer neural network with STDP learning
"""

import numpy as np
from neurons import LIFNeuron


class BipartiteNetwork:
    """
    Bipartite neural network with spike-timing dependent plasticity.
    
    This class implements a two-layer neural network where Layer A contains inhibitory
    neurons and Layer B contains excitatory neurons. The network features bidirectional
    connectivity with STDP learning rules that modify synaptic weights based on
    spike timing correlations.
    
    Attributes:
        n_A (int): Number of neurons in Layer A (inhibitory)
        n_B (int): Number of neurons in Layer B (excitatory)
        connection_prob (float): Probability of connection between any two neurons
        layer_A (list): List of LIFNeuron objects in Layer A
        layer_B (list): List of LIFNeuron objects in Layer B
        W_BA (np.ndarray): Weight matrix from Layer B to Layer A (excitatory)
        W_AB (np.ndarray): Weight matrix from Layer A to Layer B (inhibitory)
        current_time (float): Current simulation time in ms
        dt (float): Time step for simulation in ms
        background_rate_A (float): Background Poisson input rate for Layer A in Hz
        background_rate_B (float): Background Poisson input rate for Layer B in Hz
    """
    
    def __init__(self, n_A=50, n_B=50, connection_prob=0.3):
        """
        Initialize the bipartite network with specified parameters.

        Args:
            n_A (int, optional): Number of inhibitory neurons in Layer A. Defaults to 50.
            n_B (int, optional): Number of excitatory neurons in Layer B. Defaults to 50.
            connection_prob (float, optional): Connection probability. Defaults to 0.3.
        """
        self.n_A = n_A  # Layer A (inhibitory)
        self.n_B = n_B  # Layer B (excitatory)
        self.connection_prob = connection_prob

        # Lognormal distribution parameters for weight initialization
        # W_BA (excitatory) parameters
        self.lognorm_mu_BA = 0.5  # Mean of underlying normal distribution
        self.lognorm_sigma_BA = 0.5  # Standard deviation of underlying normal distribution
        self.lognorm_scale_BA = 0.25  # Scale factor for resulting lognormal values

        # W_AB (inhibitory) parameters
        self.lognorm_mu_AB = 0.5  # Mean of underlying normal distribution
        self.lognorm_sigma_AB = 0.5  # Standard deviation of underlying normal distribution
        self.lognorm_scale_AB = 0.25  # Scale factor for resulting lognormal values

        # Create neuron populations
        self.layer_A = [LIFNeuron(i, is_excitatory=False) for i in range(n_A)]
        self.layer_B = [LIFNeuron(i, is_excitatory=True) for i in range(n_B)]

        # STDP parameters
        self.A_LTP = 0.01  # Long-term potentiation amplitude
        self.A_LTD = 0.0105  # Long-term depression amplitude (balanced to prevent runaway excitation)
        self.tau_LTP = 20.0  # LTP time constant in ms
        self.tau_LTD = 20.0  # LTD time constant in ms

        # Synaptic parameters
        self.synaptic_strength = 15.0  # Base synaptic strength

        # Initialize connectivity matrices
        self.initialize_connections()

        # Simulation parameters
        self.dt = 1.0  # Time step in ms
        self.current_time = 0.0
        self.background_rate_A = 5.0  # Background Poisson input rate for Layer A in Hz
        self.background_rate_B = 5.0  # Background Poisson input rate for Layer B in Hz

        # Pre-allocate spike arrays for performance
        self.spikes_A_arr = np.zeros(self.n_A, dtype=bool)
        self.spikes_B_arr = np.zeros(self.n_B, dtype=bool)

    def initialize_connections(self):
        """
        Initialize sparse random connectivity between layers using lognormal distribution.

        Creates two weight matrices:
        - W_BA: Excitatory connections from Layer B to Layer A (using lognormal distribution)
        - W_AB: Inhibitory connections from Layer A to Layer B (using lognormal distribution)
        """
        # W_BA: excitatory connections from B to A using lognormal distribution
        rand_matrix_BA = np.random.rand(self.n_A, self.n_B)
        lognorm_weights_BA = np.random.lognormal(
            mean=self.lognorm_mu_BA,
            sigma=self.lognorm_sigma_BA,
            size=(self.n_A, self.n_B)
        ) * self.lognorm_scale_BA
        self.W_BA = np.where(
            rand_matrix_BA < self.connection_prob,
            lognorm_weights_BA,
            0
        )

        # W_AB: inhibitory connections from A to B using lognormal distribution
        rand_matrix_AB = np.random.rand(self.n_B, self.n_A)
        lognorm_weights_AB = np.random.lognormal(
            mean=self.lognorm_mu_AB,
            sigma=self.lognorm_sigma_AB,
            size=(self.n_B, self.n_A)
        ) * self.lognorm_scale_AB
        # Make inhibitory weights negative
        self.W_AB = np.where(
            rand_matrix_AB < self.connection_prob,
            -lognorm_weights_AB,  # Negative for inhibitory connections
            0
        )

    def update_lognormal_parameters(self, mu_BA=None, sigma_BA=None, scale_BA=None,
                                   mu_AB=None, sigma_AB=None, scale_AB=None):
        """
        Update lognormal distribution parameters and regenerate weight matrices.

        Args:
            mu_BA (float, optional): Mean for W_BA lognormal distribution
            sigma_BA (float, optional): Standard deviation for W_BA lognormal distribution
            scale_BA (float, optional): Scale factor for W_BA lognormal distribution
            mu_AB (float, optional): Mean for W_AB lognormal distribution
            sigma_AB (float, optional): Standard deviation for W_AB lognormal distribution
            scale_AB (float, optional): Scale factor for W_AB lognormal distribution
        """
        # Update W_BA parameters if provided
        if mu_BA is not None:
            self.lognorm_mu_BA = mu_BA
        if sigma_BA is not None:
            self.lognorm_sigma_BA = sigma_BA
        if scale_BA is not None:
            self.lognorm_scale_BA = scale_BA

        # Update W_AB parameters if provided
        if mu_AB is not None:
            self.lognorm_mu_AB = mu_AB
        if sigma_AB is not None:
            self.lognorm_sigma_AB = sigma_AB
        if scale_AB is not None:
            self.lognorm_scale_AB = scale_AB

        # Regenerate weight matrices with new parameters
        self.initialize_connections()

    def poisson_input(self, rate_hz, dt, n_neurons):
        """
        Generate Poisson spike train for a population of neurons.
        
        Args:
            rate_hz (float): Firing rate in Hz
            dt (float): Time step in ms
            n_neurons (int): Number of neurons
            
        Returns:
            np.ndarray: Boolean array indicating which neurons received input
        """
        prob = rate_hz * (dt / 1000.0)
        return np.random.random(n_neurons) < prob
    
    def apply_stdp(self, pre_neuron, post_neuron, t_post):
        """
        Apply spike-timing dependent plasticity rule.
        
        Calculates weight change based on the timing difference between
        pre- and post-synaptic spikes using exponential STDP windows.
        
        Args:
            pre_neuron (LIFNeuron): Pre-synaptic neuron
            post_neuron (LIFNeuron): Post-synaptic neuron
            t_post (float): Time of post-synaptic spike
            
        Returns:
            float: Weight change (delta_w)
        """
        delta_w = 0.0
        # Check against all recent pre-synaptic spikes
        for t_pre in pre_neuron.spike_times:
            dt_spike = t_post - t_pre
            if 0 < dt_spike < 5 * self.tau_LTP:  # Causal window (LTP)
                delta_w += self.A_LTP * np.exp(-dt_spike / self.tau_LTP)
            elif -5 * self.tau_LTD < dt_spike < 0:  # Anti-causal window (LTD)
                delta_w -= self.A_LTD * np.exp(dt_spike / self.tau_LTD)
        return delta_w

    def update(self):
        """
        Update network for one time step.

        This method performs the following operations:
        1. Calculate synaptic inputs for all neurons
        2. Update neuron states and detect spikes
        3. Apply STDP learning rules to modify weights
        4. Clip weights to valid ranges

        Returns:
            tuple: (spiked_A_indices, spiked_B_indices) - Lists of neuron indices that spiked
        """
        self.current_time += self.dt

        # --- 1. Calculate Synaptic Inputs (Optimized with NumPy) ---
        I_A = np.zeros(self.n_A)
        I_B = np.zeros(self.n_B)

        # Add background Poisson input
        I_A += self.poisson_input(self.background_rate_A, self.dt, self.n_A) * self.synaptic_strength * 0.5
        I_B += self.poisson_input(self.background_rate_B, self.dt, self.n_B) * self.synaptic_strength * 0.5

        # Add recurrent synaptic inputs from previous time step's spikes
        I_A += (self.W_BA @ self.spikes_B_arr) * self.synaptic_strength
        I_B += (self.W_AB @ self.spikes_A_arr) * self.synaptic_strength

        # --- 2. Update Neuron States and Get Spikes ---
        self.spikes_A_arr.fill(False)
        self.spikes_B_arr.fill(False)
        spiked_A_indices = []
        spiked_B_indices = []

        for i, neuron in enumerate(self.layer_A):
            if neuron.update(self.dt, I_A[i], self.current_time):
                self.spikes_A_arr[i] = True
                spiked_A_indices.append(i)

        for i, neuron in enumerate(self.layer_B):
            if neuron.update(self.dt, I_B[i], self.current_time):
                self.spikes_B_arr[i] = True
                spiked_B_indices.append(i)

        # --- 3. Apply STDP ---
        # B -> A (Excitatory) connections
        for post_idx in spiked_A_indices:
            post_neuron = self.layer_A[post_idx]
            for pre_idx in range(self.n_B):
                if self.W_BA[post_idx, pre_idx] > 0:
                    pre_neuron = self.layer_B[pre_idx]
                    if pre_neuron.spike_times:
                        delta_w = self.apply_stdp(pre_neuron, post_neuron, self.current_time)
                        self.W_BA[post_idx, pre_idx] += delta_w

        # A -> B (Inhibitory) connections (iSTDP can be complex, here we use a simple rule)
        for post_idx in spiked_B_indices:
            post_neuron = self.layer_B[post_idx]
            for pre_idx in range(self.n_A):
                if self.W_AB[post_idx, pre_idx] < 0:
                    pre_neuron = self.layer_A[pre_idx]
                    if pre_neuron.spike_times:
                        # A simple homeostatic rule for inhibition: potentiate if post-synaptic neuron is too active
                        # This is a placeholder for more complex iSTDP
                        delta_w = self.apply_stdp(pre_neuron, post_neuron, self.current_time)
                        self.W_AB[post_idx, pre_idx] += delta_w  # Note: delta_w is negative for depression

        # Clip weights to valid ranges
        self.W_BA = np.clip(self.W_BA, 0.0, 1.0)
        self.W_AB = np.clip(self.W_AB, -1.0, 0.0)

        return spiked_A_indices, spiked_B_indices
